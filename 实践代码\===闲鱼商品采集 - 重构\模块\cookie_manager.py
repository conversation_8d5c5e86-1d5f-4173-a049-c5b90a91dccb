import os
import json
import threading
import time
from typing import Optional

# 尝试导入DrissionPage，如果失败则尝试playwright
try:
    from DrissionPage import Chromium
    DRISSIONPAGE_AVAILABLE = True
    PLAYWRIGHT_AVAILABLE = False
    print("✅ 检测到DrissionPage库，将使用DrissionPage获取Cookie")
except ImportError:
    DRISSIONPAGE_AVAILABLE = False
    # 尝试导入playwright作为备选方案
    try:
        from playwright.sync_api import sync_playwright
        PLAYWRIGHT_AVAILABLE = True
        print("⚠️ DrissionPage未安装，将使用Playwright获取Cookie")
    except ImportError:
        PLAYWRIGHT_AVAILABLE = False
        print("❌ DrissionPage和Playwright都未安装，无法自动获取Cookie")

from .config_manager import ConfigManager

class CookieManager:
    def __init__(self):
        self.config = None
        self.cookie = ""  # 默认为空字符串而不是None
        self.config_directory = os.path.dirname(os.path.abspath(__file__))
        self.cookie_config_file = os.path.join(self.config_directory, 'cookie_config.json')

        # 添加线程锁和状态管理
        self._refresh_lock = threading.Lock()
        self._is_refreshing = False
        self._last_refresh_time = 0
        self._refresh_cooldown = 30  # 30秒内不重复刷新
        
    def set_config_directory(self, config_directory: str) -> None:
        """设置配置文件保存目录"""
        self.config_directory = config_directory
        self.cookie_config_file = os.path.join(self.config_directory, 'cookie_config.json')

        # 初始化配置管理器
        self.config = ConfigManager()
        self.config.set_config_directory(self.config_directory)
        self.cookie = self.config.get_cookie()
        
    def get_cookie(self) -> str:
        """从配置文件获取Cookie"""
        try:
            # 如果配置未初始化，先尝试从配置管理器获取
            if self.config is not None:
                return self.config.get_cookie()

            # 否则从cookie配置文件获取
            if os.path.exists(self.cookie_config_file):
                with open(self.cookie_config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config.get('cookie', '')
            return ''
        except Exception as e:
            print(f"加载cookie配置文件失败: {e}")
            return ''
            
    def save_cookie(self, cookie: str) -> bool:
        """保存Cookie到配置文件"""
        try:
            with open(self.cookie_config_file, 'w', encoding='utf-8') as f:
                json.dump({'cookie': cookie}, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"保存cookie配置文件失败: {e}")
            return False
            
    def refresh_cookie(self, need_login: bool = False) -> Optional[str]:
        """使用浏览器自动获取新Cookie（线程安全版本）

        Args:
            need_login: 是否需要登录获取完整Cookie，默认False（仅访问页面获取基础Cookie）
        """
        if not DRISSIONPAGE_AVAILABLE and not PLAYWRIGHT_AVAILABLE:
            print("错误: DrissionPage和Playwright都未安装，无法使用自动Cookie获取功能")
            print("推荐安装DrissionPage: pip install DrissionPage")
            print("或安装Playwright: pip install playwright && playwright install chromium")
            return None

        # 检查冷却时间，避免频繁刷新
        current_time = time.time()
        if current_time - self._last_refresh_time < self._refresh_cooldown:
            print(f"Cookie刷新冷却中，请等待 {self._refresh_cooldown - (current_time - self._last_refresh_time):.0f} 秒")
            # 返回当前Cookie，可能仍然有效
            return self.get_cookie()

        # 尝试获取锁，如果其他线程正在刷新，则等待
        with self._refresh_lock:
            # 双重检查：可能在等待锁的过程中，其他线程已经完成了刷新
            if self._is_refreshing:
                print("其他线程正在刷新Cookie，等待完成...")
                return self.get_cookie()

            # 再次检查冷却时间（可能在等待锁的过程中已经被其他线程刷新过）
            current_time = time.time()
            if current_time - self._last_refresh_time < self._refresh_cooldown:
                print("Cookie已被其他线程刷新，使用最新Cookie")
                return self.get_cookie()

            # 设置刷新状态
            self._is_refreshing = True

            try:
                print("正在启动浏览器获取Cookie...")
                if need_login:
                    print("模式: 需要登录获取完整Cookie")
                else:
                    print("模式: 仅访问页面获取基础Cookie")

                # 优先使用DrissionPage，如果不可用则使用Playwright
                if DRISSIONPAGE_AVAILABLE:
                    return self._do_refresh_cookie_drissionpage(need_login)
                else:
                    return self._do_refresh_cookie_playwright(need_login)
            finally:
                # 无论成功还是失败，都要重置刷新状态
                self._is_refreshing = False
                self._last_refresh_time = time.time()

    def _do_refresh_cookie_drissionpage(self, need_login: bool = False) -> Optional[str]:
        """使用DrissionPage实际执行Cookie刷新的方法"""
        try:
            print("🚀 正在使用DrissionPage启动浏览器...")

            # 连接到9222端口的浏览器（如果没有会自动启动）
            browser = Chromium(9222)
            print("✅ 已连接到浏览器")

            # 获取最新的标签页，如果没有则新建一个
            try:
                tab = browser.latest_tab
                if not tab.states.is_alive:
                    tab = browser.new_tab()
                    print("📄 新建标签页")
                else:
                    print("📄 使用现有标签页")
            except:
                tab = browser.new_tab()
                print("📄 新建标签页")

            # 激活标签页
            tab.set.activate()
            print("🎯 标签页已激活")

            # 访问闲鱼页面
            print("🌐 正在访问闲鱼页面...")
            success = tab.get("https://2.taobao.com/")

            if not success:
                print("❌ 访问闲鱼页面失败")
                return None

            print("✅ 页面访问成功")

            if need_login:
                print("⚠️ 需要登录模式：请在浏览器中完成登录...")
                print("💡 登录完成后，程序将自动获取Cookie")

                # 等待用户登录，检查页面标题变化或URL变化
                print("⏳ 等待登录完成...")
                try:
                    # 等待页面标题包含用户信息或URL变化，最多等待5分钟
                    tab.wait.title_change('闲鱼', timeout=300)
                    print("✅ 检测到登录状态变化")
                except:
                    print("⚠️ 等待超时，将使用当前页面的Cookie")
            else:
                print("📄 仅获取基础Cookie模式")
                # 等待页面完全加载
                tab.wait.doc_loaded()
                print("✅ 页面加载完成")

            # 使用DrissionPage的cookies()方法获取Cookie
            print("🍪 正在获取Cookie...")
            cookies_list = tab.cookies(all_domains=True, all_info=False)

            if not cookies_list:
                print("❌ 未获取到任何Cookie")
                return None

            # 转换为字符串格式
            cookie_str = cookies_list.as_str()

            if cookie_str:
                print(f"✅ 成功获取Cookie，长度: {len(cookie_str)} 字符")

                # 检查是否包含重要的Cookie字段
                important_cookies = ['_m_h5_tk', 'cookie2', '_tb_token_', 'sgcookie']
                found_cookies = []

                for cookie_name in important_cookies:
                    if f'{cookie_name}=' in cookie_str:
                        found_cookies.append(cookie_name)

                if found_cookies:
                    print(f"✅ 包含重要Cookie字段: {', '.join(found_cookies)}")

                    # 保存Cookie
                    if self.save_cookie(cookie_str):
                        self.cookie = cookie_str
                        print("💾 Cookie保存成功！")
                        return cookie_str
                    else:
                        print("❌ Cookie保存失败")
                        return None
                else:
                    print("⚠️ 获取到的Cookie缺少重要字段，可能需要登录")
                    if not need_login:
                        print("💡 建议使用need_login=True参数重新获取")
                    return cookie_str  # 即使缺少字段也返回，让调用者决定是否使用
            else:
                print("❌ Cookie转换为字符串失败")
                return None

        except Exception as e:
            print(f"❌ 使用DrissionPage获取Cookie时发生错误: {e}")
            return None

    def _do_refresh_cookie_playwright(self, need_login: bool = False) -> Optional[str]:
        """使用Playwright实际执行Cookie刷新的方法（备选方案）"""

        try:
            with sync_playwright() as p:
                # 启动浏览器
                print("正在启动Chrome浏览器...")
                browser = p.chromium.launch(
                    headless=False,  # 显示浏览器窗口
                    args=[
                        '--disable-blink-features=AutomationControlled',
                        '--disable-web-security',
                        '--disable-features=VizDisplayCompositor'
                    ]
                )

                context = browser.new_context(
                    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36",
                    viewport={'width': 1280, 'height': 720}
                )

                page = context.new_page()

                # 访问闲鱼页面
                print("正在访问闲鱼页面...")
                page.goto("https://2.taobao.com/", wait_until='networkidle')

                if need_login:
                    print("请在浏览器中完成登录...")
                    print("登录完成后，请关闭浏览器窗口")

                    # 等待用户手动登录
                    try:
                        page.wait_for_url("**/2.taobao.com/**", timeout=300000)  # 等待5分钟
                    except:
                        print("等待超时或用户关闭了浏览器")
                else:
                    print("正在获取基础Cookie...")
                    # 等待页面加载完成
                    page.wait_for_timeout(3000)

                # 获取所有Cookie
                cookies = context.cookies()

                # 构建Cookie字符串
                cookie_str = ""
                important_cookies = ['_m_h5_tk', 'cookie2', '_tb_token_', 'sgcookie', 'unb', 'uc1', 'uc3', 'uc4', 'mtop_partitioned_detect']

                for cookie in cookies:
                    # 获取所有闲鱼相关域名的Cookie
                    if (cookie['domain'] in ['.taobao.com', '2.taobao.com', '.goofish.com', 'h5api.m.goofish.com'] or
                        cookie['name'] in important_cookies):
                        cookie_str += f"{cookie['name']}={cookie['value']}; "

                # 移除末尾的分号和空格
                cookie_str = cookie_str.rstrip('; ')

                # 关闭浏览器
                browser.close()
                print("浏览器已关闭")

                if cookie_str:
                    # 检查是否包含基本的Cookie字段
                    has_basic_cookies = any(field in cookie_str for field in ['cookie2=', 'mtop_partitioned_detect='])

                    if has_basic_cookies:
                        # 保存新的Cookie
                        if self.save_cookie(cookie_str):
                            # 更新内存中的Cookie
                            self.cookie = cookie_str
                            print("Cookie获取并保存成功！")
                            print(f"Cookie长度: {len(cookie_str)} 字符")

                            # 显示获取到的主要Cookie字段
                            if '_m_h5_tk=' in cookie_str:
                                print("✓ 包含_m_h5_tk字段（API调用令牌）")
                            if 'cookie2=' in cookie_str:
                                print("✓ 包含cookie2字段（基础认证）")
                            if 'mtop_partitioned_detect=' in cookie_str:
                                print("✓ 包含mtop_partitioned_detect字段")

                            return cookie_str
                        else:
                            print("Cookie保存失败")
                            return None
                    else:
                        print("获取到的Cookie缺少必要字段，可能需要刷新页面或稍后重试")
                        return None
                else:
                    print("未能获取到任何Cookie")
                    return None

        except Exception as e:
            print(f"获取Cookie时发生错误: {e}")
            return None
