import os
import json
import configparser
from typing import Dict, Any, List, Optional

class ConfigManager:
    def __init__(self, config_file: str = '闲鱼配置.ini'):
        self.config_file_name = config_file
        self.config = configparser.ConfigParser()
        self.config_directory = os.path.dirname(os.path.abspath(__file__))
        # 初始化时不加载配置，等待设置配置目录后再加载
        self.config_file = None
        
    def set_config_directory(self, config_directory: str) -> None:
        """设置配置文件保存目录"""
        self.config_directory = config_directory
        self.config_file = os.path.join(self.config_directory, self.config_file_name)
        self.load_config()
        
    def load_config(self) -> None:
        """加载配置文件"""
        if self.config_file is None:
            return  # 配置文件路径未设置，不进行加载

        if os.path.exists(self.config_file):
            self.config.read(self.config_file, encoding='utf-8')
        else:
            self.create_default_config()
            self.save_config()
            
    def create_default_config(self) -> None:
        """创建默认配置"""
        self.config['系统设置'] = {
            '用户代理': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            '应用密钥': '12574478',
            '接口名称': 'mtop.taobao.idle.shop.user.items',
            '基础URL': 'https://h5api.m.taobao.com/h5/mtop.taobao.idle.shop.user.items/1.0/'
        }
            
    def save_config(self) -> None:
        """保存配置到文件"""
        if self.config_file is None:
            return  # 配置文件路径未设置，不进行保存

        # 确保配置目录存在
        os.makedirs(self.config_directory, exist_ok=True)

        with open(self.config_file, 'w', encoding='utf-8') as f:
            self.config.write(f)
            
    def get_system_settings(self) -> Dict[str, str]:
        """获取系统设置"""
        if '系统设置' not in self.config:
            self.create_default_config()
            self.save_config()
            
        return {
            '用户代理': self.config['系统设置'].get('用户代理'),
            '应用密钥': self.config['系统设置'].get('应用密钥'),
            '接口名称': self.config['系统设置'].get('接口名称'),
            '基础URL': self.config['系统设置'].get('基础URL')
        }
        
    def get_all_sellers(self) -> List[Dict[str, str]]:
        """获取所有卖家信息"""
        seller_list = []
        
        for section_name in self.config.sections():
            if section_name.startswith('卖家_'):
                seller_id = section_name.split('_')[1]
                
                # 尝试获取卖家名称，兼容不同的大小写配置
                try:
                    # 先尝试标准的"卖家名称"
                    if '卖家名称' in self.config[section_name]:
                        seller_name = self.config[section_name]['卖家名称']
                    # 再尝试小写的"卖家名称"
                    elif '卖家名称'.lower() in [k.lower() for k in self.config[section_name]]:
                        for key in self.config[section_name]:
                            if key.lower() == '卖家名称'.lower():
                                seller_name = self.config[section_name][key]
                                break
                    else:
                        seller_name = '未知'
                except:
                    seller_name = '未知'
                
                # 打印调试信息
                print(f"找到卖家: {seller_id}, 名称: {seller_name}")
                
                seller_info = {
                    '卖家ID': seller_id,
                    '卖家名称': seller_name
                }
                seller_list.append(seller_info)
        
        # 打印总数
        print(f"共找到 {len(seller_list)} 个卖家")
        return seller_list
        
    def get_seller_list(self) -> List[Dict[str, str]]:
        """获取卖家列表（别名方法）"""
        return self.get_all_sellers()
        
    def get_default_settings(self) -> Dict[str, str]:
        """获取默认设置"""
        # 返回一些默认值
        return {
            '默认卖家ID': '2219812363424',
            '默认卖家名称': '糖糖steam电玩',
            '默认分组ID': '51959993',
            '默认分组名称': '综合'
        }
        
    def add_seller(self, seller_id: str, seller_name: str) -> bool:
        """添加卖家"""
        try:
            section_name = f'卖家_{seller_id}'
            if section_name not in self.config:
                self.config.add_section(section_name)
            
            self.config[section_name]['卖家名称'] = seller_name
            self.save_config()
            return True
        except Exception as e:
            print(f"添加卖家失败: {e}")
            return False
            
    def delete_seller(self, seller_id: str) -> bool:
        """删除卖家"""
        try:
            section_name = f'卖家_{seller_id}'
            if section_name in self.config:
                self.config.remove_section(section_name)
                self.save_config()
                return True
            return False
        except Exception as e:
            print(f"删除卖家失败: {e}")
            return False
            
    def get_seller_name(self, seller_id: str) -> Optional[str]:
        """获取卖家名称"""
        section_name = f'卖家_{seller_id}'
        if section_name in self.config:
            return self.config[section_name].get('卖家名称', '未知卖家')
        return None
        
    def get_cookie(self) -> str:
        try:
            cookie_config_file = os.path.join(self.config_directory, 'cookie_config.json')
            if os.path.exists(cookie_config_file):
                with open(cookie_config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config.get('cookie', '')
            return ''
        except Exception as e:
            print(f"加载cookie配置文件失败: {e}")
            return ''
            
    def save_cookie(self, cookie: str) -> bool:
        try:
            cookie_config_file = os.path.join(self.config_directory, 'cookie_config.json')
            with open(cookie_config_file, 'w', encoding='utf-8') as f:
                json.dump({'cookie': cookie}, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"保存cookie配置文件失败: {e}")
            return False
