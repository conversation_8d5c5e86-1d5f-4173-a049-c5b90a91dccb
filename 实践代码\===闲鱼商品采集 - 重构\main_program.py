import sys
import time
import threading
import os
from typing import Dict, List, Any
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from 模块.data_collector import DataCollector
from 模块.interface import create_interface
from 模块.config_manager import ConfigManager
from 模块.cookie_manager import CookieManager

class MainProgram:
    def __init__(self):
        # 创建必要的文件夹结构
        self.create_folder_structure()
        
        # 初始化组件
        self.config_manager = ConfigManager()
        self.config_manager.set_config_directory(self.config_directory)
        
        self.cookie_manager = <PERSON>ieManager()
        self.cookie_manager.set_config_directory(self.config_directory)
        
        self.data_collector = DataCollector()
        self.data_collector.set_cookie_manager(self.cookie_manager)
        
        self.interface = create_interface(self)
        self.collection_results = []
        self.collection_status = "空闲"
        self.current_seller_id = ""
        
    def create_folder_structure(self):
        """创建项目所需的文件夹结构"""
        # 定义文件夹路径
        self.project_root_directory = os.path.dirname(os.path.abspath(__file__))
        self.data_directory = os.path.join(self.project_root_directory, "数据")
        self.seller_data_directory = os.path.join(self.data_directory, "卖家数据")
        self.config_directory = os.path.join(self.project_root_directory, "配置")
        self.log_directory = os.path.join(self.project_root_directory, "日志")

        # 创建文件夹
        for directory in [self.data_directory, self.seller_data_directory, self.config_directory, self.log_directory]:
            if not os.path.exists(directory):
                os.makedirs(directory)
                
        # 移动现有配置文件到配置目录
        self.move_existing_files()
        
    def move_existing_files(self):
        """移动现有文件到对应目录"""
        # 移动配置文件
        source_file_list = [
            ('闲鱼配置.ini', self.config_directory),
            ('cookie_config.json', self.config_directory),
            ('api_keys.txt', self.config_directory)
        ]

        for source_file, target_directory in source_file_list:
            source_path = os.path.join(self.project_root_directory, source_file)
            target_path = os.path.join(target_directory, source_file)

            if os.path.exists(source_path) and not os.path.exists(target_path):
                try:
                    # 复制文件而不是移动，避免破坏现有功能
                    with open(source_path, 'r', encoding='utf-8') as source:
                        with open(target_path, 'w', encoding='utf-8') as target:
                            target.write(source.read())
                    print(f"已复制文件 {source_file} 到 {target_directory}")
                except Exception as e:
                    print(f"复制文件 {source_file} 时出错: {e}")

        # 移动卖家数据文件
        for file_name in os.listdir(self.project_root_directory):
            if file_name.startswith("卖家_") and file_name.endswith(".json"):
                source_path = os.path.join(self.project_root_directory, file_name)
                target_path = os.path.join(self.seller_data_directory, file_name)

                if not os.path.exists(target_path):
                    try:
                        # 复制文件而不是移动
                        with open(source_path, 'r', encoding='utf-8') as source:
                            with open(target_path, 'w', encoding='utf-8') as target:
                                target.write(source.read())
                        print(f"已复制文件 {file_name} 到 {self.seller_data_directory}")
                    except Exception as e:
                        print(f"复制文件 {file_name} 时出错: {e}")
        
    def start_collection(self, seller_id: str, page_count: int, only_collect_wanted: bool, only_save_id: bool) -> None:
        """开始采集数据"""
        if self.collection_status == "运行中":
            return
        
        self.collection_status = "运行中"
        self.current_seller_id = seller_id
        self.collection_results = []
        
        # 创建一个新线程来执行采集
        collection_thread = threading.Thread(
            target=self._execute_collection,
            args=(seller_id, page_count, only_collect_wanted, only_save_id)
        )
        collection_thread.daemon = True
        collection_thread.start()
    
    def _execute_collection(self, seller_id: str, page_count: int, only_collect_wanted: bool, only_save_id: bool) -> None:
        """在线程中执行采集"""
        try:
            self.interface.update_status(f"开始采集卖家 {seller_id} 的商品数据...")
            
            collection_results = []
            
            # 如果页数为0，表示采集全部页面
            current_page = 1
            max_pages = page_count if page_count > 0 else float('inf')  # 如果页数为0，设置一个无限大的值
            
            while current_page <= max_pages:
                result = self.data_collector.crawl_seller_page(current_page, seller_id)
                
                if isinstance(result, str):
                    if result == "NO_MORE_DATA":
                        self.interface.update_status(f"已到达最后一页，共采集 {current_page-1} 页")
                        break
                    elif result == "COOKIE_EXPIRED" or result == "COOKIE_ERROR":
                        self.interface.update_status("Cookie已过期或无效，请更新Cookie")
                        break
                    elif result == "LIMIT_REACHED":
                        self.interface.update_status(f"已达到最大可查看页数限制，共采集 {current_page-1} 页")
                        break
                    else:
                        self.interface.update_status(f"采集第 {current_page} 页时出错: {result}")
                        current_page += 1
                        continue
                
                # 过滤数据（如果需要）
                if only_collect_wanted:
                    result = [product for product in result if 'want_count' in product]
                
                collection_results.extend(result)
                
                self.interface.update_status(f"已采集第 {current_page} 页，当前共有 {len(collection_results)} 个商品")
                current_page += 1
                # 移除等待时间，实现快速采集
            
            if len(collection_results) > 0:
                # 保存为JSON格式
                file_path = self.save_collection_data(collection_results, seller_id)
                self.interface.update_status(f"商品数据已保存到: {file_path}")
                self.interface.update_status(f"采集完成，共采集到 {len(collection_results)} 个商品")
            else:
                self.interface.update_status("采集完成，但未获取到任何商品数据")
            
        except Exception as e:
            self.interface.update_status(f"采集过程中出现错误: {e}")
        finally:
            self.collection_status = "空闲"
            
    def start_batch_collection(self, seller_id_list: List[str], page_count: int, only_collect_wanted: bool, only_save_id: bool, use_multithreading: bool = True) -> None:
        """开始批量采集多个卖家的数据"""
        if self.collection_status == "运行中":
            return

        self.collection_status = "运行中"

        # 创建一个新线程来执行批量采集
        collection_thread = threading.Thread(
            target=self._execute_batch_collection,
            args=(seller_id_list, page_count, only_collect_wanted, only_save_id, use_multithreading)
        )
        collection_thread.daemon = True
        collection_thread.start()
        
    def _execute_batch_collection(self, seller_id_list: List[str], page_count: int, only_collect_wanted: bool, only_save_id: bool, use_multithreading: bool = True) -> None:
        """在线程中执行批量采集"""
        try:
            total_sellers = len(seller_id_list)
            
            if use_multithreading:
                self.interface.update_status(f"开始多线程批量采集 {total_sellers} 个卖家的商品数据...")
                self._execute_multithreaded_batch_collection(seller_id_list, page_count, only_collect_wanted, only_save_id)
            else:
                self.interface.update_status(f"开始单线程批量采集 {total_sellers} 个卖家的商品数据...")
                self._execute_singlethreaded_batch_collection(seller_id_list, page_count, only_collect_wanted, only_save_id)
                
        except Exception as e:
            self.interface.update_status(f"批量采集过程中出现错误: {e}")
        finally:
            self.collection_status = "空闲"

    def _execute_multithreaded_batch_collection(self, seller_id_list: List[str], page_count: int, only_collect_wanted: bool, only_save_id: bool) -> None:
        """执行多线程批量采集"""
        total_sellers = len(seller_id_list)

        # 创建线程锁用于同步状态更新
        status_lock = threading.Lock()
        completion_count = {"count": 0}

        # 使用线程池执行器进行并发采集
        max_threads = min(total_sellers, 8)  # 最多8个线程同时运行，避免过多请求

        with ThreadPoolExecutor(max_workers=max_threads) as executor:
            # 为每个卖家创建独立的数据采集器实例
            future_to_seller = {}

            for seller_id in seller_id_list:
                if self.collection_status != "运行中":
                    break

                # 创建独立的数据采集器实例
                collector = DataCollector()
                collector.set_cookie_manager(self.cookie_manager)

                # 提交采集任务
                future = executor.submit(
                    self._execute_single_seller_collection,
                    seller_id, page_count, only_collect_wanted, only_save_id, collector, status_lock, completion_count, total_sellers
                )
                future_to_seller[future] = seller_id

            # 等待所有任务完成
            for future in as_completed(future_to_seller):
                seller_id = future_to_seller[future]
                try:
                    future.result()  # 获取结果，如果有异常会抛出
                except Exception as e:
                    with status_lock:
                        self.interface.update_status(f"卖家 {seller_id} 采集失败: {e}")

                # 检查是否被用户停止
                if self.collection_status != "运行中":
                    self.interface.update_status("采集已被用户停止")
                    break

        self.interface.update_status("多线程批量采集完成")

    def _execute_singlethreaded_batch_collection(self, seller_id_list: List[str], page_count: int, only_collect_wanted: bool, only_save_id: bool) -> None:
        """执行单线程批量采集（原有逻辑）"""
        total_sellers = len(seller_id_list)

        for index, seller_id in enumerate(seller_id_list):
            if self.collection_status != "运行中":
                self.interface.update_status("采集已被用户停止")
                break

            self.interface.update_status(f"正在采集第 {index+1}/{total_sellers} 个卖家 (ID: {seller_id})...")
            self.current_seller_id = seller_id

            # 执行单个卖家的采集
            self._execute_collection(seller_id, page_count, only_collect_wanted, only_save_id)

        self.interface.update_status("单线程批量采集完成")

    def _execute_single_seller_collection(self, seller_id: str, page_count: int, only_collect_wanted: bool, only_save_id: bool,
                        collector: DataCollector, status_lock: threading.Lock, completion_count: Dict, total_sellers: int) -> None:
        """在多线程环境中执行单个卖家的采集"""
        try:
            with status_lock:
                self.interface.update_status(f"开始采集卖家 {seller_id} 的商品数据...")

            collection_results = []

            # 如果页数为0，表示采集全部页面
            current_page = 1
            max_pages = page_count if page_count > 0 else float('inf')

            while current_page <= max_pages:
                # 检查是否被用户停止
                if self.collection_status != "运行中":
                    break

                result = collector.crawl_seller_page(current_page, seller_id)

                if isinstance(result, str):
                    if result == "NO_MORE_DATA":
                        with status_lock:
                            self.interface.update_status(f"卖家 {seller_id}: 已到达最后一页，共采集 {current_page-1} 页")
                        break
                    elif result == "COOKIE_EXPIRED" or result == "COOKIE_ERROR":
                        with status_lock:
                            self.interface.update_status(f"卖家 {seller_id}: Cookie已过期或无效")
                        break
                    elif result == "LIMIT_REACHED":
                        with status_lock:
                            self.interface.update_status(f"卖家 {seller_id}: 已达到最大可查看页数限制，共采集 {current_page-1} 页")
                        break
                    else:
                        with status_lock:
                            self.interface.update_status(f"卖家 {seller_id}: 采集第 {current_page} 页时出错: {result}")
                        current_page += 1
                        continue

                # 过滤数据（如果需要）
                if only_collect_wanted:
                    result = [product for product in result if 'want_count' in product]

                collection_results.extend(result)

                with status_lock:
                    self.interface.update_status(f"卖家 {seller_id}: 已采集第 {current_page} 页，当前共有 {len(collection_results)} 个商品")
                current_page += 1

            if len(collection_results) > 0:
                # 保存为JSON格式
                file_path = self.save_collection_data(collection_results, seller_id)
                with status_lock:
                    completion_count["count"] += 1
                    self.interface.update_status(f"卖家 {seller_id}: 采集完成，共采集到 {len(collection_results)} 个商品 ({completion_count['count']}/{total_sellers})")
                    self.interface.update_status(f"卖家 {seller_id}: 数据已保存到: {file_path}")
            else:
                with status_lock:
                    completion_count["count"] += 1
                    self.interface.update_status(f"卖家 {seller_id}: 采集完成，但未获取到任何商品数据 ({completion_count['count']}/{total_sellers})")

        except Exception as e:
            with status_lock:
                completion_count["count"] += 1
                self.interface.update_status(f"卖家 {seller_id}: 采集过程中出现错误: {e} ({completion_count['count']}/{total_sellers})")

    def collect_all_sellers_oneclick(self, page_count: int, only_collect_wanted: bool, only_save_id: bool, use_multithreading: bool = True) -> None:
        """一键采集所有卖家的数据

        Args:
            page_count: 采集页数，0表示全部页面
            only_collect_wanted: 是否只采集有想要数量的商品
            only_save_id: 是否只保存商品ID（暂未使用）
            use_multithreading: 是否使用多线程并发采集，默认True
        """
        seller_list = self.config_manager.get_all_sellers()
        seller_id_list = [seller['卖家ID'] for seller in seller_list]

        if not seller_id_list:
            self.interface.update_status("没有找到卖家信息，请先添加卖家")
            return

        if use_multithreading:
            self.interface.update_status(f"共加载了 {len(seller_id_list)} 个卖家，将使用多线程并发采集")
        else:
            self.interface.update_status(f"共加载了 {len(seller_id_list)} 个卖家，将使用单线程顺序采集")

        self.start_batch_collection(seller_id_list, page_count, only_collect_wanted, only_save_id, use_multithreading)

    def save_collection_data(self, collection_results: List[Dict], seller_id: str) -> str:
        """保存采集数据到JSON文件"""
        import json
        from datetime import datetime

        # 获取卖家名称
        seller_name = self.config_manager.get_seller_name(seller_id) or "未知卖家"

        # 构造保存数据
        save_data = {
            "卖家信息": {
                "卖家ID": seller_id,
                "卖家名称": seller_name,
                "采集时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "商品总数": len(collection_results)
            },
            "商品列表": collection_results
        }

        # 保存到文件
        file_name = f"卖家_{seller_id}.json"
        file_path = os.path.join(self.seller_data_directory, file_name)

        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)

        return file_path

    def get_file_path(self, file_type: str, file_name: str) -> str:
        """根据文件类型获取文件的完整路径"""
        if file_type == "卖家数据":
            return os.path.join(self.seller_data_directory, file_name)
        elif file_type == "配置":
            return os.path.join(self.config_directory, file_name)
        elif file_type == "日志":
            return os.path.join(self.log_directory, file_name)
        else:
            return os.path.join(self.project_root_directory, file_name)

if __name__ == "__main__":
    app = MainProgram()
    app.interface.root.mainloop()  # 修改这里，调用root对象的mainloop方法
